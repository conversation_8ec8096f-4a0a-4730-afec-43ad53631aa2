=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-16 14:47:59
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-47-59-PM.txt
==================================================

[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-47-59-PM.txt
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 14:47:59
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,151 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,153 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,153 - interactive_pipeline_01 - WARNING - ⚠️ Failed to evaluate auto-start for Stage 02: cannot access local variable '_get' where it is not associated with a value
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,153 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,153 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,154 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,154 - interactive_pipeline_01 - INFO -    📊 Loaded 0 existing movie records
[2025-09-16 14:47:59] [STDERR] [+0:00:00] 2025-09-16 14:47:59,154 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:47:59] [STDOUT] [+0:00:00]   4. Quit
[2025-09-16 14:47:59] [STDOUT] [+0:00:00] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 🤖 Processing Mode Selection
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] How would you like to handle download decisions?
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 📝 Full Auto Mode Details:
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01]    • Automatically selects preflight analysis for every item
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01]    • Automatically chooses max candidates when prompted
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01]    • No manual intervention required - perfect for overnight processing
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:00] [STDOUT] [+0:00:01]    • Falls back gracefully if preflight fails
[2025-09-16 14:48:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] ✅ Manual Mode selected - you'll be prompted for each item
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 📁 Loaded 6 movies from C:\Users\<USER>\Videos\PlexAutomator\new_movie_requests.txt
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] ============================================================
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 🎬 Movies Available for Processing:
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] ============================================================
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]    1. 13 Going on 30 (2004)
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]    2. Don't Breathe (2016)
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]    3. Top Gun: Maverick (2022)
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]    4. There Will Be Blood (2007)
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]    5. Star Trek Into Darkness (2013)
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]    6. The Dark Knight (2008)
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 📝 Selection Options:
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]   • Single: Enter number (e.g., '3')
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]   • All: Enter 'all' or 'a'
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]   • None: Enter 'none' or 'n' to skip
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:06] [STDOUT] [+0:00:07]   • Quit: Enter 'quit' or 'q'
[2025-09-16 14:48:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 🎬 Processing 6 selected movies...
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] ============================================================
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,484 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 📍 Progress: 1/6
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 🎬 Processing: 13 Going on 30 (2004)
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,485 - interactive_pipeline_01 - INFO - Processing movie: 13 Going on 30 (2004)
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,486 - utils.metadata_apis - INFO - Using enhanced fuzzy matching for: '13 Going on 30 (2004)'
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,489 - _internal.utils.fuzzy_matching - INFO - Starting enhanced fuzzy matching for: '13 Going on 30 (2004)' (type: movie)
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,643 - utils.metadata_apis - INFO - TMDb search for '13 Going on 30' (Year: 2004) found 1 results.
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,643 - _internal.utils.fuzzy_matching - INFO - Found 1 candidates for '13 Going on 30 (2004)' via TMDb
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,782 - _internal.utils.fuzzy_matching - INFO - Fast path: Exact title match - '13 Going on 30' (2004)
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,782 - _internal.utils.fuzzy_matching - INFO - Fast-path match found for '13 Going on 30 (2004)' (0.29s)
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,782 - utils.metadata_apis - INFO - Fetching details for TMDb ID 10096
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,939 - utils.metadata_apis - INFO - Fetched details for TMDb ID 10096: 13 Going on 30
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,940 - utils.metadata_apis - INFO - Successfully matched '13 Going on 30 (2004)' to '13 Going on 30' (97.0% confidence) via fast_path in 0.29s
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] ✅ Found metadata: 13 Going on 30 (2004)
[2025-09-16 14:48:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:48:08] [STDERR] [+0:00:09] 2025-09-16 14:48:08,940 - interactive_pipeline_01 - INFO - Successfully found metadata for: 13 Going on 30 (2004)
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,268 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,269 - interactive_pipeline_01 - INFO - Searching Radarr for: 13 Going on 30 2004
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,400 - interactive_pipeline_01 - INFO - Found match: 13 Going on 30 (2004)
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,411 - interactive_pipeline_01 - INFO - 🔍 Checking for duplicates: TMDB ID 10096 in 0 existing movies
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,411 - interactive_pipeline_01 - INFO - ✅ NO DUPLICATE: Movie TMDB 10096 not found in Radarr - safe to add
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,413 - interactive_pipeline_01 - INFO - 🔁 Using existing Radarr root folder: E:\
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,413 - interactive_pipeline_01 - INFO - 📋 ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,414 - interactive_pipeline_01 - INFO - 🎬 Adding movie to Radarr with 1 quality profile(s): 13 Going on 30 2004
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,414 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 4 (searchForMovie=False)...
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,453 - interactive_pipeline_01 - INFO -    ✅ Successfully added: 13 Going on 30 2004 (ID: 683, Profile: 4)
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 📥 Queued "13 Going on 30 (2004)" for download...
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,454 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T14:48:09.454939", "event": "download_queued", "job_id": "radarr_683", "title": "13 Going on 30 (2004)", "source": "radarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "radarr_id": 683, "quality": "Unknown"}
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,455 - interactive_pipeline_01 - INFO - 📋 Enhanced tracking: 13 Going on 30 (2004)
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,455 - interactive_pipeline_01 - INFO -    🆔 Radarr ID: 683
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,455 - interactive_pipeline_01 - INFO -    📊 Job ID: radarr_683
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 📊 Movie queued for download: 13 Going on 30 (2004)
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10]    🔬 Enhanced tracking: radarr_6...
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10]    🆔 Radarr ID: 683
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10]    🛡️ Fallback protection: Enabled
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,456 - interactive_pipeline_01 - INFO - Phase 1: Enhanced telemetry job started: radarr_683 for movie 683
[2025-09-16 14:48:09] [STDERR] [+0:00:10] 2025-09-16 14:48:09,456 - interactive_pipeline_01 - INFO - Movie ID 683 tracked for accurate correlation
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 🤔 Download Strategy Choice for: 13 Going on 30
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] Choose how you want to handle downloads for this movie:
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 3. ⏭️  Skip - Add to Radarr but don't start any downloads yet
[2025-09-16 14:48:09] [STDOUT] [+0:00:10] 
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,954 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 🐛 DEBUG: sanitized movie_title = '13_Going_on_30'
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 🐛 DEBUG: final out_path = 'workspace\preflight_decisions\movies\13_Going_on_30.json'
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,954 - interactive_pipeline_01 - INFO - 🧪 Running movie preflight analysis for Radarr ID 683
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,954 - interactive_pipeline_01 - INFO - 🎯 Quality constraint: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 🔍 Fetching movie releases from Radarr...
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 🎯 Quality filter: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:48:15] [STDOUT] [+0:00:16] 
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,956 - preflight_analyzer.cache_observability - INFO - Initialized cache metrics with max_events=10000
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,956 - preflight_analyzer.memory_cache - INFO - Initialized memory cache with maxsize=1000, ttl=43200s
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,958 - preflight_analyzer.persistent_cache - INFO - Initialized persistent cache at workspace\preflight_cache\cache\analysis_cache.db
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,958 - preflight_analyzer.guid_reconciler - INFO - Initialized GUID reconciler with size_tolerance=0.05, title_threshold=0.8, min_confidence=0.7
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,958 - preflight_analyzer.multi_layer_cache - INFO - Initialized multi-layer cache at workspace\preflight_cache\cache
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,958 - preflight_analyzer.cache - INFO - Initialized decision cache at workspace\preflight_cache\cache
[2025-09-16 14:48:15] [STDERR] [+0:00:16] 2025-09-16 14:48:15,958 - preflight_analyzer.history_store - INFO - Initialized DecisionHistory with decision cache (legacy path: workspace\preflight_cache\decision_history.json)
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 📊 Raw releases fetched: 39
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] ✅ No duplicates found, proceeding with 39 unique releases
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 🎯 Filtering releases for strategy: 1080p_only
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 🔍 Quality filter applied: 37 releases match 1080p-only strategy
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 🔬 Dynamic scanning: analyzing all 37 available candidates
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 🎬 Analyzing 22 movie candidates in parallel (max 6 concurrent)...
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:16] [STDOUT] [+0:00:17]    🔍 14:48:16 Analyzing: 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:16] [STDOUT] [+0:00:17]    🔍 14:48:16 Analyzing: 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-16 14:48:16] [STDOUT] [+0:00:17] 
[2025-09-16 14:48:17] [STDOUT] [+0:00:18]    🔍 14:48:17 Analyzing: 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-16 14:48:17] [STDOUT] [+0:00:18] 
[2025-09-16 14:48:18] [STDOUT] [+0:00:18]    🔍 14:48:18 Analyzing: 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-16 14:48:18] [STDOUT] [+0:00:18] 
[2025-09-16 14:48:18] [STDOUT] [+0:00:19]    🔍 14:48:18 Analyzing: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:48:18] [STDOUT] [+0:00:19] 
[2025-09-16 14:48:19] [STDOUT] [+0:00:19]    🔍 14:48:19 Analyzing: 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-16 14:48:19] [STDOUT] [+0:00:19] 
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    ✅ 14:48:28 Result: ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,691 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c066bed4-d82a-4fa7-a51e-db10d4c02f10 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,691 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c066bed4..., 1.2ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,691 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c066bed4..., 1.2ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO) → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,692 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: f4c81580-7902-4773-b6ee-6b72861db6de -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,692 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f4c81580..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,692 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f4c81580..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,693 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: bd017ed0-a7fa-4b9b-98f0-4be32e105a74 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,694 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd017ed0..., 0.9ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,694 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd017ed0..., 0.9ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,695 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: d173e331-20ee-42db-999e-33ee72531214 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,695 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d173e331..., 0.7ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,695 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d173e331..., 0.7ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.720p.BluRay.x264-METiS → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,695 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: bd698dd9-423f-4227-b26b-8547458a55bd -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,696 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd698dd9..., 0.7ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,696 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd698dd9..., 0.7ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-OFT → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,697 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9019f0ab-2760-46ab-972e-a05d937d7731 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,697 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9019f0ab..., 0.9ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,697 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9019f0ab..., 0.9ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-nikt0 → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,698 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 026fc6a5-f476-4b20-92f6-7f5b66803d1e -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,698 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 026fc6a5..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,698 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 026fc6a5..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,699 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 29d9fc87-9b8c-4cf5-a9dd-7a79471cb861 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,699 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 29d9fc87..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,699 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 29d9fc87..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,700 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8615657d-ffb9-404a-96d1-a60fca8a8aeb -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,700 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8615657d..., 0.9ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,700 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8615657d..., 0.9ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,701 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e4ddc8ff-7938-41bb-87ff-a6a746b950eb -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,701 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e4ddc8ff..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,701 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e4ddc8ff..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-METiS → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,702 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2b8e1907-83fa-4c1e-898f-3caaa8b9b748 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,702 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2b8e1907..., 0.7ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,702 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2b8e1907..., 0.7ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13 Going on 30 2004.1080p.AC3-NoGroup → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,703 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: fb3849e6-21a8-4f51-9905-aa814600625f -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,703 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: fb3849e6..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,703 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: fb3849e6..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,704 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 69a1f5a5-dd0c-4e7b-84bf-41416a6fcf59 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,704 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 69a1f5a5..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,704 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 69a1f5a5..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,705 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c87f0349-de71-4345-b98b-fe2fd2c84596 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,705 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c87f0349..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,705 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c87f0349..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,706 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9c172b36-1468-4cc0-9d69-cf8de2f0e614 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,706 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9c172b36..., 0.8ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,706 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9c172b36..., 0.8ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,707 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 7286961e-8aea-49c2-aa4c-28becf64f811 -> movie:unknown
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,707 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7286961e..., 0.7ms)
[2025-09-16 14:48:28] [STDERR] [+0:00:29] 2025-09-16 14:48:28,707 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7286961e..., 0.7ms)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29]    💾 14:48:28 Cache hit: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR → ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 14:48:28] [STDOUT] [+0:00:29] 
[2025-09-16 14:48:33] [STDOUT] [+0:00:33]    ✅ 14:48:33 Result: ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:48:33] [STDOUT] [+0:00:33] 
[2025-09-16 14:48:35] [STDOUT] [+0:00:36]    ✅ 14:48:35 Result: ACCEPT (risk: 0.0219, missing: 0.3%)
[2025-09-16 14:48:35] [STDOUT] [+0:00:36] 
[2025-09-16 14:48:35] [STDOUT] [+0:00:36]    ✅ 14:48:35 Result: ACCEPT (risk: 0.0005, missing: 0.0%)
[2025-09-16 14:48:35] [STDOUT] [+0:00:36] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    ✅ 14:48:36 Result: ACCEPT (risk: 0.0153, missing: 0.0%)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    ✅ 14:48:36 Result: ACCEPT (risk: 0.0054, missing: 0.0%)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 💾 Cache performance: 16/22 hits (72.7%) - saved significant analysis time!
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 📊 Analysis complete: 22 valid, 0 errors
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 🏆 Best candidate: 2.34 GB | ACCEPT | risk: 0.0054 | missing: 0.0%
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDERR] [+0:00:37] 2025-09-16 14:48:36,919 - interactive_pipeline_01 - INFO - 📝 Movie preflight decision saved: workspace\preflight_decisions\movies\13_Going_on_30.json
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 💾 Cache performance: 16/22 hits (72.7%) - saved significant analysis time!
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 📊 Preflight Results: 22 analyzed, 22 acceptable, 0 errors
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 📊 Combined Results: 1 total movie analyzed, 22 acceptable releases found
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 🔬 Movie Preflight Analysis Results:
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #1. 🎬 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 0.28 GB (300,389,461 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #2. 🎬 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 1.73 GB (1,860,169,468 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0153 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #3. 🎬 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 2.27 GB (2,436,351,791 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0219 | Missing: 0.3% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #4. 🎬 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 2.27 GB (2,436,930,115 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0005 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #5. 🎬 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 2.34 GB (2,517,640,816 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0054 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #6. 🎬 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 2.63 GB (2,828,226,322 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #7. 🎬 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 4.56 GB (4,897,553,218 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #8. 🎬 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 4.59 GB (4,923,901,037 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #9. 🎬 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 4.64 GB (4,984,523,381 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #10. 🎬 13.Going.on.30.2004.720p.BluRay.x264-METiS
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 4.78 GB (5,136,435,650 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #11. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-OFT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 5.12 GB (5,493,911,649 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #12. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-nikt0
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 5.12 GB (5,494,056,722 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #13. 🎬 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 5.46 GB (5,859,882,678 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #14. 🎬 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 8.10 GB (8,699,365,146 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #15. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 8.61 GB (9,247,582,818 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #16. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-METiS
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 9.02 GB (9,688,109,054 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #17. 🎬 13 Going on 30 2004.1080p.AC3-NoGroup
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 9.09 GB (9,762,208,782 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #18. 🎬 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 10.50 GB (11,273,989,818 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #19. 🎬 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 10.77 GB (11,566,818,530 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #20. 🎬 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 21.12 GB (22,675,404,920 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #21. 🎬 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 22.84 GB (24,525,709,753 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    #22. 🎬 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        💾 Size: 22.93 GB (24,616,529,445 bytes)
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 🔬 Preflight selection (best candidate):
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    • 2.34 GB  |  ACCEPT  |  risk: 0.0054  |  missing: 0.0%
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    • Runtime: 98 min
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    • Release: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]    ⚠️ Sanity: Candidate looks suspiciously small for its quality
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:48:36] [STDOUT] [+0:00:37]       - 1080p 1.4 GB/h below expected
[2025-09-16 14:48:36] [STDOUT] [+0:00:37] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 🔍 DEBUG: Checking candidate storage conditions...
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16]    telemetry_integrator: True
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16]    telemetry_integrator.telemetry: True
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16]    best: True
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16]    all_candidates: True
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 💾 Storing candidate information for fallback system...
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDERR] [+0:02:16] 2025-09-16 14:50:15,612 - interactive_pipeline_01 - INFO - 📄 Stored candidate info for Radarr ID 683
[2025-09-16 14:50:15] [STDERR] [+0:02:16] 2025-09-16 14:50:15,612 - interactive_pipeline_01 - INFO -    🎯 Candidate: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:50:15] [STDERR] [+0:02:16] 2025-09-16 14:50:15,612 - interactive_pipeline_01 - INFO -    👤 User selection index: 4
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] ✅ Stored candidate #5 of 22 acceptable candidates for fallback
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] ------------------------------------------------------------
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 🏁 Terminal logging ended for 01_intake_and_nzb_search
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 🕐 Ended at: 2025-09-16 14:50:15
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] ⏱️ Total duration: 0:02:16.465978
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-47-59-PM.txt
[2025-09-16 14:50:15] [STDOUT] [+0:02:16] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 01_intake_and_nzb_search
Ended: 2025-09-16 14:50:15
Duration: 0:02:16.465978
==================================================
