=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-16 14:53:58
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-53-58-PM.txt
==================================================

[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-53-58-PM.txt
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 14:53:58
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,953 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,954 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,955 - interactive_pipeline_01 - WARNING - ⚠️ Failed to evaluate auto-start for Stage 02: cannot access local variable '_get' where it is not associated with a value
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,955 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,955 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,955 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,956 - interactive_pipeline_01 - INFO -    📊 Loaded 1 existing movie records
[2025-09-16 14:53:58] [STDERR] [+0:00:00] 2025-09-16 14:53:58,956 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:53:58] [STDOUT] [+0:00:00]   4. Quit
[2025-09-16 14:53:58] [STDOUT] [+0:00:00] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 🤖 Processing Mode Selection
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] How would you like to handle download decisions?
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 📝 Full Auto Mode Details:
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01]    • Automatically selects preflight analysis for every item
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01]    • Automatically chooses max candidates when prompted
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01]    • No manual intervention required - perfect for overnight processing
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:00] [STDOUT] [+0:00:01]    • Falls back gracefully if preflight fails
[2025-09-16 14:54:00] [STDOUT] [+0:00:01] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] ✅ Manual Mode selected - you'll be prompted for each item
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 📁 Loaded 6 movies from C:\Users\<USER>\Videos\PlexAutomator\new_movie_requests.txt
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] ============================================================
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 🎬 Movies Available for Processing:
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] ============================================================
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]    1. 13 Going on 30 (2004)
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]    2. Don't Breathe (2016)
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]    3. Top Gun: Maverick (2022)
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]    4. There Will Be Blood (2007)
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]    5. Star Trek Into Darkness (2013)
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]    6. The Dark Knight (2008)
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 📝 Selection Options:
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]   • Single: Enter number (e.g., '3')
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]   • All: Enter 'all' or 'a'
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]   • None: Enter 'none' or 'n' to skip
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:02] [STDOUT] [+0:00:03]   • Quit: Enter 'quit' or 'q'
[2025-09-16 14:54:02] [STDOUT] [+0:00:03] 
[2025-09-16 14:54:06] [STDOUT] [+0:00:07] ✅ Selected 1 movies:
[2025-09-16 14:54:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:54:06] [STDOUT] [+0:00:07]     1. 13 Going on 30 (2004)
[2025-09-16 14:54:06] [STDOUT] [+0:00:07] 
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 🎬 Processing 1 selected movies...
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] ============================================================
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,689 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 📍 Progress: 1/1
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 🎬 Processing: 13 Going on 30 (2004)
[2025-09-16 14:54:07] [STDOUT] [+0:00:08] 
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,689 - interactive_pipeline_01 - INFO - Processing movie: 13 Going on 30 (2004)
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,690 - utils.metadata_apis - INFO - Using enhanced fuzzy matching for: '13 Going on 30 (2004)'
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,692 - _internal.utils.fuzzy_matching - INFO - Starting enhanced fuzzy matching for: '13 Going on 30 (2004)' (type: movie)
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,825 - utils.metadata_apis - INFO - TMDb search for '13 Going on 30' (Year: 2004) found 1 results.
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,825 - _internal.utils.fuzzy_matching - INFO - Found 1 candidates for '13 Going on 30 (2004)' via TMDb
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,948 - _internal.utils.fuzzy_matching - INFO - Fast path: Exact title match - '13 Going on 30' (2004)
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,948 - _internal.utils.fuzzy_matching - INFO - Fast-path match found for '13 Going on 30 (2004)' (0.26s)
[2025-09-16 14:54:07] [STDERR] [+0:00:08] 2025-09-16 14:54:07,948 - utils.metadata_apis - INFO - Fetching details for TMDb ID 10096
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,065 - utils.metadata_apis - INFO - Fetched details for TMDb ID 10096: 13 Going on 30
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,066 - utils.metadata_apis - INFO - Successfully matched '13 Going on 30 (2004)' to '13 Going on 30' (97.0% confidence) via fast_path in 0.26s
[2025-09-16 14:54:08] [STDOUT] [+0:00:09] ✅ Found metadata: 13 Going on 30 (2004)
[2025-09-16 14:54:08] [STDOUT] [+0:00:09] 
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,068 - interactive_pipeline_01 - INFO - Successfully found metadata for: 13 Going on 30 (2004)
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,071 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,071 - interactive_pipeline_01 - INFO - Searching Radarr for: 13 Going on 30 2004
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,160 - interactive_pipeline_01 - INFO - Found match: 13 Going on 30 (2004)
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,171 - interactive_pipeline_01 - INFO - 🔍 Checking for duplicates: TMDB ID 10096 in 1 existing movies
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,172 - interactive_pipeline_01 - INFO - 📊 Radarr TMDB IDs: [10096]
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,172 - interactive_pipeline_01 - INFO - 🎯 DUPLICATE FOUND: Movie TMDB 10096 exists in Radarr with ID 683
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,172 - interactive_pipeline_01 - INFO - 🔍 Movie exists in Radarr, verifying filesystem reality: 13 Going on 30 2004
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,172 - interactive_pipeline_01 - WARNING - 🧹 ORPHANED: Movie exists in Radarr but NOT in filesystem: 13 Going on 30 2004
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,172 - interactive_pipeline_01 - INFO - 🧹 Cleaning up orphaned Radarr entry to enable fresh download...
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,179 - interactive_pipeline_01 - INFO - ✅ Removed orphaned movie from Radarr: 13 Going on 30 2004
[2025-09-16 14:54:08] [STDERR] [+0:00:09] 2025-09-16 14:54:08,179 - interactive_pipeline_01 - INFO - ⏳ Waiting 2 seconds for Radarr to process cleanup...
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,183 - interactive_pipeline_01 - INFO - 🔍 Comprehensive verification: Checking Radarr + SABnzbd...
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,193 - interactive_pipeline_01 - INFO - ✅ Radarr verification: Movie removed from library
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,194 - interactive_pipeline_01 - INFO - ✅ Radarr queue verification: No queue items found
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,201 - interactive_pipeline_01 - INFO - ✅ SABnzbd verification: No history entries found
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,202 - interactive_pipeline_01 - INFO - ✅ VERIFICATION COMPLETE: Movie fully removed, safe to proceed
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,202 - interactive_pipeline_01 - INFO - 🔄 Starting fresh download...
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,204 - interactive_pipeline_01 - INFO - 🔁 Using existing Radarr root folder: E:\
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,204 - interactive_pipeline_01 - INFO - 📋 ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,204 - interactive_pipeline_01 - INFO - 🎬 Adding movie to Radarr with 1 quality profile(s): 13 Going on 30 2004
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,204 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 4 (searchForMovie=False)...
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,256 - interactive_pipeline_01 - INFO -    ✅ Successfully added: 13 Going on 30 2004 (ID: 684, Profile: 4)
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 📥 Queued "13 Going on 30 (2004)" for download...
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,258 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T14:54:10.258066", "event": "download_queued", "job_id": "radarr_684", "title": "13 Going on 30 (2004)", "source": "radarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "radarr_id": 684, "quality": "Unknown"}
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,258 - interactive_pipeline_01 - INFO - 📋 Enhanced tracking: 13 Going on 30 (2004)
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,258 - interactive_pipeline_01 - INFO -    🆔 Radarr ID: 684
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,258 - interactive_pipeline_01 - INFO -    📊 Job ID: radarr_684
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 📊 Movie queued for download: 13 Going on 30 (2004)
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11]    🔬 Enhanced tracking: radarr_6...
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11]    🆔 Radarr ID: 684
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11]    🛡️ Fallback protection: Enabled
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,259 - interactive_pipeline_01 - INFO - Phase 1: Enhanced telemetry job started: radarr_684 for movie 684
[2025-09-16 14:54:10] [STDERR] [+0:00:11] 2025-09-16 14:54:10,259 - interactive_pipeline_01 - INFO - Movie ID 684 tracked for accurate correlation
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 🤔 Download Strategy Choice for: 13 Going on 30
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] Choose how you want to handle downloads for this movie:
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 3. ⏭️  Skip - Add to Radarr but don't start any downloads yet
[2025-09-16 14:54:10] [STDOUT] [+0:00:11] 
[2025-09-16 14:54:12] [STDERR] [+0:00:13] 2025-09-16 14:54:12,020 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 🐛 DEBUG: sanitized movie_title = '13_Going_on_30'
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 🐛 DEBUG: final out_path = 'workspace\preflight_decisions\movies\13_Going_on_30.json'
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDERR] [+0:00:13] 2025-09-16 14:54:12,020 - interactive_pipeline_01 - INFO - ✅ Found existing preflight decision: workspace\preflight_decisions\movies\13_Going_on_30.json
[2025-09-16 14:54:12] [STDERR] [+0:00:13] 2025-09-16 14:54:12,021 - interactive_pipeline_01 - INFO - 📝 Loaded existing movie preflight decision for: 13_Going_on_30
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] ✅ Using existing preflight decision for: 13 Going on 30
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 💾 Cache performance: 16/22 hits (72.7%) - saved significant analysis time!
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 📊 Preflight Results: 22 analyzed, 22 acceptable, 0 errors
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 📊 Combined Results: 1 total movie analyzed, 22 acceptable releases found
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 🔬 Movie Preflight Analysis Results:
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #1. 🎬 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 0.28 GB (300,389,461 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #2. 🎬 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 1.73 GB (1,860,169,468 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0153 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #3. 🎬 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 2.27 GB (2,436,351,791 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0219 | Missing: 0.3% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #4. 🎬 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 2.27 GB (2,436,930,115 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0005 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #5. 🎬 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 2.34 GB (2,517,640,816 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0054 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #6. 🎬 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 2.63 GB (2,828,226,322 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #7. 🎬 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 4.56 GB (4,897,553,218 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #8. 🎬 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 4.59 GB (4,923,901,037 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #9. 🎬 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 4.64 GB (4,984,523,381 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #10. 🎬 13.Going.on.30.2004.720p.BluRay.x264-METiS
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 4.78 GB (5,136,435,650 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #11. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-OFT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 5.12 GB (5,493,911,649 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #12. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-nikt0
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 5.12 GB (5,494,056,722 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #13. 🎬 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 5.46 GB (5,859,882,678 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #14. 🎬 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 8.10 GB (8,699,365,146 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #15. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 8.61 GB (9,247,582,818 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #16. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-METiS
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 9.02 GB (9,688,109,054 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #17. 🎬 13 Going on 30 2004.1080p.AC3-NoGroup
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 9.09 GB (9,762,208,782 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #18. 🎬 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 10.50 GB (11,273,989,818 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #19. 🎬 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 10.77 GB (11,566,818,530 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #20. 🎬 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 21.12 GB (22,675,404,920 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #21. 🎬 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 22.84 GB (24,525,709,753 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    #22. 🎬 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        💾 Size: 22.93 GB (24,616,529,445 bytes)
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 🔬 Preflight selection (best candidate):
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    • 2.34 GB  |  ACCEPT  |  risk: 0.0054  |  missing: 0.0%
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    • Runtime: 98 min
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    • Release: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]    ⚠️ Sanity: Candidate looks suspiciously small for its quality
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:54:12] [STDOUT] [+0:00:13]       - 1080p 1.4 GB/h below expected
[2025-09-16 14:54:12] [STDOUT] [+0:00:13] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 🔍 DEBUG: Checking candidate storage conditions...
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20]    telemetry_integrator: True
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20]    telemetry_integrator.telemetry: True
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20]    best: True
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20]    all_candidates: True
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 💾 Storing candidate information for fallback system...
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDERR] [+0:01:20] 2025-09-16 14:55:19,657 - interactive_pipeline_01 - INFO - 📄 Stored candidate info for Radarr ID 684
[2025-09-16 14:55:19] [STDERR] [+0:01:20] 2025-09-16 14:55:19,658 - interactive_pipeline_01 - INFO -    🎯 Candidate: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:55:19] [STDERR] [+0:01:20] 2025-09-16 14:55:19,658 - interactive_pipeline_01 - INFO -    👤 User selection index: 4
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] ✅ Stored candidate #5 of 22 acceptable candidates for fallback
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] ------------------------------------------------------------
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 🏁 Terminal logging ended for 01_intake_and_nzb_search
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 🕐 Ended at: 2025-09-16 14:55:19
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] ⏱️ Total duration: 0:01:20.709410
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-53-58-PM.txt
[2025-09-16 14:55:19] [STDOUT] [+0:01:20] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 01_intake_and_nzb_search
Ended: 2025-09-16 14:55:19
Duration: 0:01:20.709410
==================================================
