=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-16 14:56:11
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-56-11-PM.txt
==================================================

[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_02-56-11-PM.txt
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 14:56:11
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,062 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,063 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,064 - interactive_pipeline_01 - WARNING - ⚠️ Failed to evaluate auto-start for Stage 02: cannot access local variable '_get' where it is not associated with a value
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,064 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,064 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,064 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,064 - interactive_pipeline_01 - INFO -    📊 Loaded 2 existing movie records
[2025-09-16 14:56:11] [STDERR] [+0:00:00] 2025-09-16 14:56:11,064 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:11] [STDOUT] [+0:00:00]   4. Quit
[2025-09-16 14:56:11] [STDOUT] [+0:00:00] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 🤖 Processing Mode Selection
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] How would you like to handle download decisions?
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 📝 Full Auto Mode Details:
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01]    • Automatically selects preflight analysis for every item
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01]    • Automatically chooses max candidates when prompted
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01]    • No manual intervention required - perfect for overnight processing
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:12] [STDOUT] [+0:00:01]    • Falls back gracefully if preflight fails
[2025-09-16 14:56:12] [STDOUT] [+0:00:01] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] ✅ Manual Mode selected - you'll be prompted for each item
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 📁 Loaded 6 movies from C:\Users\<USER>\Videos\PlexAutomator\new_movie_requests.txt
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] ============================================================
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 🎬 Movies Available for Processing:
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] ============================================================
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]    1. 13 Going on 30 (2004)
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]    2. Don't Breathe (2016)
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]    3. Top Gun: Maverick (2022)
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]    4. There Will Be Blood (2007)
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]    5. Star Trek Into Darkness (2013)
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]    6. The Dark Knight (2008)
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 📝 Selection Options:
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]   • Single: Enter number (e.g., '3')
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]   • All: Enter 'all' or 'a'
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]   • None: Enter 'none' or 'n' to skip
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:13] [STDOUT] [+0:00:02]   • Quit: Enter 'quit' or 'q'
[2025-09-16 14:56:13] [STDOUT] [+0:00:02] 
[2025-09-16 14:56:14] [STDOUT] [+0:00:03] ✅ Selected 1 movies:
[2025-09-16 14:56:14] [STDOUT] [+0:00:03] 
[2025-09-16 14:56:14] [STDOUT] [+0:00:03]     1. 13 Going on 30 (2004)
[2025-09-16 14:56:14] [STDOUT] [+0:00:03] 
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 🎬 Processing 1 selected movies...
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] ============================================================
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,251 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 📍 Progress: 1/1
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 🎬 Processing: 13 Going on 30 (2004)
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,251 - interactive_pipeline_01 - INFO - Processing movie: 13 Going on 30 (2004)
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,252 - utils.metadata_apis - INFO - Using enhanced fuzzy matching for: '13 Going on 30 (2004)'
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,254 - _internal.utils.fuzzy_matching - INFO - Starting enhanced fuzzy matching for: '13 Going on 30 (2004)' (type: movie)
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,386 - utils.metadata_apis - INFO - TMDb search for '13 Going on 30' (Year: 2004) found 1 results.
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,386 - _internal.utils.fuzzy_matching - INFO - Found 1 candidates for '13 Going on 30 (2004)' via TMDb
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,496 - _internal.utils.fuzzy_matching - INFO - Fast path: Exact title match - '13 Going on 30' (2004)
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,496 - _internal.utils.fuzzy_matching - INFO - Fast-path match found for '13 Going on 30 (2004)' (0.24s)
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,497 - utils.metadata_apis - INFO - Fetching details for TMDb ID 10096
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,606 - utils.metadata_apis - INFO - Fetched details for TMDb ID 10096: 13 Going on 30
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,607 - utils.metadata_apis - INFO - Successfully matched '13 Going on 30 (2004)' to '13 Going on 30' (97.0% confidence) via fast_path in 0.24s
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] ✅ Found metadata: 13 Going on 30 (2004)
[2025-09-16 14:56:15] [STDOUT] [+0:00:04] 
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,607 - interactive_pipeline_01 - INFO - Successfully found metadata for: 13 Going on 30 (2004)
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,609 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,609 - interactive_pipeline_01 - INFO - Searching Radarr for: 13 Going on 30 2004
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,711 - interactive_pipeline_01 - INFO - Found match: 13 Going on 30 (2004)
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,713 - interactive_pipeline_01 - INFO - 🔍 Checking for duplicates: TMDB ID 10096 in 1 existing movies
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,714 - interactive_pipeline_01 - INFO - 📊 Radarr TMDB IDs: [10096]
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,714 - interactive_pipeline_01 - INFO - 🎯 DUPLICATE FOUND: Movie TMDB 10096 exists in Radarr with ID 684
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,714 - interactive_pipeline_01 - INFO - 🔍 Movie exists in Radarr, verifying filesystem reality: 13 Going on 30 2004
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,714 - interactive_pipeline_01 - WARNING - 🧹 ORPHANED: Movie exists in Radarr but NOT in filesystem: 13 Going on 30 2004
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,714 - interactive_pipeline_01 - INFO - 🧹 Cleaning up orphaned Radarr entry to enable fresh download...
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,720 - interactive_pipeline_01 - INFO - ✅ Removed orphaned movie from Radarr: 13 Going on 30 2004
[2025-09-16 14:56:15] [STDERR] [+0:00:04] 2025-09-16 14:56:15,720 - interactive_pipeline_01 - INFO - ⏳ Waiting 2 seconds for Radarr to process cleanup...
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,731 - interactive_pipeline_01 - INFO - 🔍 Comprehensive verification: Checking Radarr + SABnzbd...
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,735 - interactive_pipeline_01 - INFO - ✅ Radarr verification: Movie removed from library
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,736 - interactive_pipeline_01 - INFO - ✅ Radarr queue verification: No queue items found
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,744 - interactive_pipeline_01 - INFO - ✅ SABnzbd verification: No history entries found
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,744 - interactive_pipeline_01 - INFO - ✅ VERIFICATION COMPLETE: Movie fully removed, safe to proceed
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,744 - interactive_pipeline_01 - INFO - 🔄 Starting fresh download...
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,747 - interactive_pipeline_01 - INFO - 🔁 Using existing Radarr root folder: E:\
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,747 - interactive_pipeline_01 - INFO - 📋 ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,747 - interactive_pipeline_01 - INFO - 🎬 Adding movie to Radarr with 1 quality profile(s): 13 Going on 30 2004
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,747 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 4 (searchForMovie=False)...
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,788 - interactive_pipeline_01 - INFO -    ✅ Successfully added: 13 Going on 30 2004 (ID: 685, Profile: 4)
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 📥 Queued "13 Going on 30 (2004)" for download...
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,790 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T14:56:17.790096", "event": "download_queued", "job_id": "radarr_685", "title": "13 Going on 30 (2004)", "source": "radarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "radarr_id": 685, "quality": "Unknown"}
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,790 - interactive_pipeline_01 - INFO - 📋 Enhanced tracking: 13 Going on 30 (2004)
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,790 - interactive_pipeline_01 - INFO -    🆔 Radarr ID: 685
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,790 - interactive_pipeline_01 - INFO -    📊 Job ID: radarr_685
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 📊 Movie queued for download: 13 Going on 30 (2004)
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06]    🔬 Enhanced tracking: radarr_6...
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06]    🆔 Radarr ID: 685
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06]    🛡️ Fallback protection: Enabled
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,791 - interactive_pipeline_01 - INFO - Phase 1: Enhanced telemetry job started: radarr_685 for movie 685
[2025-09-16 14:56:17] [STDERR] [+0:00:06] 2025-09-16 14:56:17,791 - interactive_pipeline_01 - INFO - Movie ID 685 tracked for accurate correlation
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 🤔 Download Strategy Choice for: 13 Going on 30
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] Choose how you want to handle downloads for this movie:
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 3. ⏭️  Skip - Add to Radarr but don't start any downloads yet
[2025-09-16 14:56:17] [STDOUT] [+0:00:06] 
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,214 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🐛 DEBUG: sanitized movie_title = '13_Going_on_30'
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🐛 DEBUG: final out_path = 'workspace\preflight_decisions\movies\13_Going_on_30.json'
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,215 - interactive_pipeline_01 - INFO - 🧪 Running movie preflight analysis for Radarr ID 685
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,215 - interactive_pipeline_01 - INFO - 🎯 Quality constraint: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🔍 Fetching movie releases from Radarr...
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🎯 Quality filter: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,216 - preflight_analyzer.cache_observability - INFO - Initialized cache metrics with max_events=10000
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,216 - preflight_analyzer.memory_cache - INFO - Initialized memory cache with maxsize=1000, ttl=43200s
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,225 - preflight_analyzer.persistent_cache - INFO - Initialized persistent cache at workspace\preflight_cache\cache\analysis_cache.db
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,226 - preflight_analyzer.guid_reconciler - INFO - Initialized GUID reconciler with size_tolerance=0.05, title_threshold=0.8, min_confidence=0.7
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,226 - preflight_analyzer.multi_layer_cache - INFO - Initialized multi-layer cache at workspace\preflight_cache\cache
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,226 - preflight_analyzer.cache - INFO - Initialized decision cache at workspace\preflight_cache\cache
[2025-09-16 14:56:19] [STDERR] [+0:00:08] 2025-09-16 14:56:19,226 - preflight_analyzer.history_store - INFO - Initialized DecisionHistory with decision cache (legacy path: workspace\preflight_cache\decision_history.json)
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 📊 Raw releases fetched: 39
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] ✅ No duplicates found, proceeding with 39 unique releases
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🎯 Filtering releases for strategy: 1080p_only
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🔍 Quality filter applied: 37 releases match 1080p-only strategy
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🔬 Dynamic scanning: analyzing all 37 available candidates
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 🎬 Analyzing 22 movie candidates in parallel (max 6 concurrent)...
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:19] [STDOUT] [+0:00:08]    🔍 14:56:19 Analyzing: 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-16 14:56:19] [STDOUT] [+0:00:08] 
[2025-09-16 14:56:20] [STDOUT] [+0:00:09]    🔍 14:56:20 Analyzing: 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-16 14:56:20] [STDOUT] [+0:00:09] 
[2025-09-16 14:56:20] [STDOUT] [+0:00:09]    🔍 14:56:20 Analyzing: 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-16 14:56:20] [STDOUT] [+0:00:09] 
[2025-09-16 14:56:22] [STDOUT] [+0:00:11]    🔍 14:56:22 Analyzing: 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-16 14:56:22] [STDOUT] [+0:00:11] 
[2025-09-16 14:56:22] [STDOUT] [+0:00:11]    🔍 14:56:22 Analyzing: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:56:22] [STDOUT] [+0:00:11] 
[2025-09-16 14:56:23] [STDOUT] [+0:00:12]    🔍 14:56:23 Analyzing: 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-16 14:56:23] [STDOUT] [+0:00:12] 
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    ✅ 14:56:32 Result: ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,502 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c066bed4-d82a-4fa7-a51e-db10d4c02f10 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,502 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c066bed4..., 1.1ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,502 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c066bed4..., 1.1ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO) → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,503 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: f4c81580-7902-4773-b6ee-6b72861db6de -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,503 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f4c81580..., 0.9ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,503 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f4c81580..., 0.9ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,504 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: bd017ed0-a7fa-4b9b-98f0-4be32e105a74 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,505 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd017ed0..., 0.9ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,505 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd017ed0..., 0.9ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,506 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: d173e331-20ee-42db-999e-33ee72531214 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,506 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d173e331..., 0.9ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,506 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d173e331..., 0.9ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.720p.BluRay.x264-METiS → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,507 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: bd698dd9-423f-4227-b26b-8547458a55bd -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,508 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd698dd9..., 0.9ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,508 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bd698dd9..., 0.9ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-OFT → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,509 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9019f0ab-2760-46ab-972e-a05d937d7731 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,509 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9019f0ab..., 0.8ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,509 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9019f0ab..., 0.8ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-nikt0 → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,510 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 026fc6a5-f476-4b20-92f6-7f5b66803d1e -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,510 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 026fc6a5..., 0.8ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,510 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 026fc6a5..., 0.8ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,511 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 29d9fc87-9b8c-4cf5-a9dd-7a79471cb861 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,511 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 29d9fc87..., 0.7ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,511 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 29d9fc87..., 0.7ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,512 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8615657d-ffb9-404a-96d1-a60fca8a8aeb -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,512 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8615657d..., 0.9ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,512 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8615657d..., 0.9ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,513 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e4ddc8ff-7938-41bb-87ff-a6a746b950eb -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,513 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e4ddc8ff..., 0.9ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,513 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e4ddc8ff..., 0.9ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.BluRay.x264-METiS → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,514 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2b8e1907-83fa-4c1e-898f-3caaa8b9b748 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,514 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2b8e1907..., 0.7ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,514 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2b8e1907..., 0.7ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13 Going on 30 2004.1080p.AC3-NoGroup → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,515 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: fb3849e6-21a8-4f51-9905-aa814600625f -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,515 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: fb3849e6..., 0.7ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,515 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: fb3849e6..., 0.7ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,516 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 69a1f5a5-dd0c-4e7b-84bf-41416a6fcf59 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,516 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 69a1f5a5..., 0.7ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,516 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 69a1f5a5..., 0.7ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,517 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c87f0349-de71-4345-b98b-fe2fd2c84596 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,517 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c87f0349..., 0.7ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,517 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c87f0349..., 0.7ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,518 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9c172b36-1468-4cc0-9d69-cf8de2f0e614 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,518 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9c172b36..., 0.8ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,518 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9c172b36..., 0.8ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,519 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 7286961e-8aea-49c2-aa4c-28becf64f811 -> movie:unknown
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,519 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7286961e..., 0.7ms)
[2025-09-16 14:56:32] [STDERR] [+0:00:21] 2025-09-16 14:56:32,519 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7286961e..., 0.7ms)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21]    💾 14:56:32 Cache hit: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR → ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:32] [STDOUT] [+0:00:21] 
[2025-09-16 14:56:37] [STDOUT] [+0:00:26]    ✅ 14:56:37 Result: ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 14:56:37] [STDOUT] [+0:00:26] 
[2025-09-16 14:56:39] [STDOUT] [+0:00:28]    ✅ 14:56:39 Result: ACCEPT (risk: 0.0211, missing: 0.0%)
[2025-09-16 14:56:39] [STDOUT] [+0:00:28] 
[2025-09-16 14:56:40] [STDOUT] [+0:00:29]    ✅ 14:56:40 Result: ACCEPT (risk: 0.0153, missing: 0.0%)
[2025-09-16 14:56:40] [STDOUT] [+0:00:29] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    ✅ 14:56:42 Result: ACCEPT (risk: 0.0013, missing: 0.3%)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    ✅ 14:56:42 Result: ACCEPT (risk: 0.0054, missing: 0.0%)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 💾 Cache performance: 16/22 hits (72.7%) - saved significant analysis time!
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 📊 Analysis complete: 22 valid, 0 errors
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 🏆 Best candidate: 2.34 GB | ACCEPT | risk: 0.0054 | missing: 0.0%
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDERR] [+0:00:31] 2025-09-16 14:56:42,752 - interactive_pipeline_01 - INFO - 📝 Movie preflight decision saved: workspace\preflight_decisions\movies\13_Going_on_30.json
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 💾 Cache performance: 16/22 hits (72.7%) - saved significant analysis time!
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 📊 Preflight Results: 22 analyzed, 22 acceptable, 0 errors
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 📊 Combined Results: 1 total movie analyzed, 22 acceptable releases found
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 🔬 Movie Preflight Analysis Results:
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #1. 🎬 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 0.28 GB (300,389,461 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #2. 🎬 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 1.73 GB (1,860,169,468 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0153 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #3. 🎬 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 2.27 GB (2,436,351,791 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0211 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #4. 🎬 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 2.27 GB (2,436,930,115 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0013 | Missing: 0.3% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #5. 🎬 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 2.34 GB (2,517,640,816 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0054 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #6. 🎬 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 2.63 GB (2,828,226,322 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #7. 🎬 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 4.56 GB (4,897,553,218 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #8. 🎬 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 4.59 GB (4,923,901,037 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #9. 🎬 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 4.64 GB (4,984,523,381 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #10. 🎬 13.Going.on.30.2004.720p.BluRay.x264-METiS
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 4.78 GB (5,136,435,650 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #11. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-OFT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 5.12 GB (5,493,911,649 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #12. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-nikt0
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 5.12 GB (5,494,056,722 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #13. 🎬 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 5.46 GB (5,859,882,678 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #14. 🎬 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 8.10 GB (8,699,365,146 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #15. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 8.61 GB (9,247,582,818 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #16. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-METiS
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 9.02 GB (9,688,109,054 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #17. 🎬 13 Going on 30 2004.1080p.AC3-NoGroup
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 9.09 GB (9,762,208,782 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #18. 🎬 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 10.50 GB (11,273,989,818 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #19. 🎬 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 10.77 GB (11,566,818,530 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #20. 🎬 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 21.12 GB (22,675,404,920 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #21. 🎬 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 22.84 GB (24,525,709,753 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    #22. 🎬 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        💾 Size: 22.93 GB (24,616,529,445 bytes)
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 🔬 Preflight selection (best candidate):
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    • 2.34 GB  |  ACCEPT  |  risk: 0.0054  |  missing: 0.0%
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    • Runtime: 98 min
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    • Release: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]    ⚠️ Sanity: Candidate looks suspiciously small for its quality
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
[2025-09-16 14:56:42] [STDOUT] [+0:00:31]       - 1080p 1.4 GB/h below expected
[2025-09-16 14:56:42] [STDOUT] [+0:00:31] 
